# data<PERSON>i kaydeden kod
import traceback

import pandas as pd
import numpy as np
from binance.client import Client
import time
from datetime import datetime


class BacktestMeta:
    def __init__(self):
        # Tek tip parametre ile birden fazla coin eklemek için:
        default_params = {
            'INTERVALS': ['15m', '30m'],
            'ATR_WINDOWS': [20],
            'ATR_MULTIPLIERS': [1.2],
            'ATR_MULTIPLIERS_ST': [2, 2.5, 3.5],
            'N_RATIOS': [(1.00, 0.00)],
            'HIGHLOW_WINDOWS': [20],
            'EXIT_PARAMS': [(40, -20, 60), (30, -20, 50), (20, -20, 40)],
        }
        coin_list = ['ENA', 'IMX', 'AVAX', 'ETH', 'BTC', 'SOL', 'POL', 'OP', 'UNI']

        self.PARITE_PARAMETRELERI = {}
        for coin in coin_list:
            self.PARITE_PARAMETRELERI[coin] = default_params.copy()

        # <PERSON><PERSON><PERSON> bazı coinler için özel parametre istiyorsan, eski yönte<PERSON> de ekleyebilirsin:
        # self.PARITE_PARAMETRELERI['TRX'] = {...}

        self.COINS = list(self.PARITE_PARAMETRELERI.keys())
        self.start_dt = datetime(2025, 1, 1, 0, 0, 0)
        self.end_dt = datetime(2025, 12, 31, 23, 59, 59)
        self.results = []
        self.client = Client()
        # Trading state variables
        self.capital = None
        self.balance = None
        self.state = None
        self.entry_price = None
        self.entry_atr = None
        self.pyramid = None
        self.pyramid_entries = None
        self.pyramid_amounts = None
        self.max_price_during_trade = None
        self.min_price_during_trade = None
        self.max_pyramid = 4
        self.commission_rate = 0.00045
        self.cikis_sebebi = None
        self.block_reason = None
        # Tracking lists for simulation
        self.states = []
        self.capitals = []
        self.balances = []
        self.amounts = []
        self.entry_prices = []
        self.pyramids = []
        self.exit_reasons = []
        self.exit_prices = []
        self.entry_block_reasons = []
        self.profits = []
        self.cumulative_profits = []
        self.commissions = []
        self.trade_profits = []
        self.n_ratio_list = []
        self.max_possible_profits = []
        self.pyramid_entry_commissions = []  # Track entry commissions per pyramid entry
        self.pyramid_exit_commissions = []  # Track exit commissions per pyramid exit
        # Ensure state variables are always initialized to valid types
        self.balance = 0.0
        self.pyramid_entries = []
        self.pyramid_amounts = []
        self.first_entry_price = None  # EKLENDİ: İlk piramit giriş fiyatı

    def rma(self, series, window):
        alpha = 1 / window
        return series.ewm(alpha=alpha, adjust=False).mean()

    def calc_atr_rma(self, df, window):
        prev_close = df['Close'].shift(1)
        tr1 = df['High'] - df['Low']
        tr2 = (df['High'] - prev_close).abs()
        tr3 = (df['Low'] - prev_close).abs()
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return self.rma(tr, window)

    def calc_supertrend(self, df, atr_window, multiplier):
        hl2 = (df['High'] + df['Low']) / 2
        atr = self.calc_atr_rma(df, atr_window)
        upperband = hl2 + multiplier * atr
        lowerband = hl2 - multiplier * atr

        final_upperband = upperband.copy()
        final_lowerband = lowerband.copy()
        supertrend = [np.nan] * len(df)
        supertrend_state = ['neutral'] * len(df)

        for i in range(len(df)):
            if i == 0:
                supertrend[i] = final_upperband.iloc[i]
                if df['High'].iloc[i] < supertrend[i]:
                    supertrend_state[i] = 'short'
                elif df['Low'].iloc[i] > supertrend[i]:
                    supertrend_state[i] = 'long'
                else:
                    supertrend_state[i] = 'short' if df['Close'].iloc[i] < supertrend[i] else 'long'
                continue

            if (upperband.iloc[i] < final_upperband.iloc[i - 1]) or (
                    df['High'].iloc[i - 1] > final_upperband.iloc[i - 1]):
                final_upperband.iloc[i] = upperband.iloc[i]
            else:
                final_upperband.iloc[i] = final_upperband.iloc[i - 1]

            if (lowerband.iloc[i] > final_lowerband.iloc[i - 1]) or (
                    df['Low'].iloc[i - 1] < final_lowerband.iloc[i - 1]):
                final_lowerband.iloc[i] = lowerband.iloc[i]
            else:
                final_lowerband.iloc[i] = final_lowerband.iloc[i - 1]

            prev_state = supertrend_state[i - 1]
            if prev_state == 'long':
                if df['Low'].iloc[i] < final_lowerband.iloc[i]:
                    supertrend[i] = final_upperband.iloc[i]
                    supertrend_state[i] = 'short'
                else:
                    supertrend[i] = final_lowerband.iloc[i]
                    supertrend_state[i] = 'long'
            elif prev_state == 'short':
                if df['High'].iloc[i] > final_upperband.iloc[i]:
                    supertrend[i] = final_lowerband.iloc[i]
                    supertrend_state[i] = 'long'
                else:
                    supertrend[i] = final_upperband.iloc[i]
                    supertrend_state[i] = 'short'
            else:
                supertrend[i] = final_upperband.iloc[i]
                if df['High'].iloc[i] < supertrend[i]:
                    supertrend_state[i] = 'short'
                elif df['Low'].iloc[i] > supertrend[i]:
                    supertrend_state[i] = 'long'
                else:
                    supertrend_state[i] = 'short' if df['Close'].iloc[i] < supertrend[i] else 'long'

        df['supertrend'] = pd.Series(supertrend, index=df.index)
        df['supertrend_state'] = pd.Series(supertrend_state, index=df.index)
        df['supertrend_upperband'] = final_upperband
        df['supertrend_lowerband'] = final_lowerband

        return df

    def calc_turtle_state(self, df, donchian_period=20):
        df['donchian_high'] = df['High'].rolling(window=donchian_period).max().shift(1)
        df['donchian_low'] = df['Low'].rolling(window=donchian_period).min().shift(1)
        df['turtle_state'] = 'neutral'
        for i in range(1, len(df)):
            prev_state = df['turtle_state'].iloc[i - 1]
            if prev_state == 'neutral':
                if df['High'].iloc[i] > df['donchian_high'].iloc[i]:
                    df.loc[df.index[i], 'turtle_state'] = 'long'
                elif df['Low'].iloc[i] < df['donchian_low'].iloc[i]:
                    df.loc[df.index[i], 'turtle_state'] = 'short'
                else:
                    df.loc[df.index[i], 'turtle_state'] = 'neutral'
            elif prev_state == 'long':
                exit_period = donchian_period // 2
                exit_low = df['Low'].iloc[max(0, i - exit_period):i].min()
                full_switch_low = df['Low'].iloc[max(0, i - donchian_period):i].min()
                if df['Low'].iloc[i] <= full_switch_low:
                    df.loc[df.index[i], 'turtle_state'] = 'short'
                elif df['Low'].iloc[i] <= exit_low:
                    df.loc[df.index[i], 'turtle_state'] = 'neutral'
                else:
                    df.loc[df.index[i], 'turtle_state'] = 'long'
            elif prev_state == 'short':
                exit_period = donchian_period // 2
                exit_high = df['High'].iloc[max(0, i - exit_period):i].max()
                full_switch_high = df['High'].iloc[max(0, i - donchian_period):i].max()
                if df['High'].iloc[i] >= full_switch_high:
                    df.loc[df.index[i], 'turtle_state'] = 'long'
                elif df['High'].iloc[i] >= exit_high:
                    df.loc[df.index[i], 'turtle_state'] = 'neutral'
                else:
                    df.loc[df.index[i], 'turtle_state'] = 'short'
        return df

    def check_indicator_switches(self, df, i):
        turtle_switch = False
        supertrend_switch = False
        if i > 0:
            if df['turtle_state'].iloc[i] != df['turtle_state'].iloc[i - 1]:
                turtle_switch = True
            if df['supertrend_state'].iloc[i] != df['supertrend_state'].iloc[i - 1]:
                supertrend_switch = True
        return turtle_switch, supertrend_switch

    def fetch_klines(self, symbol, interval, start_time, end_time):
        all_klines = []
        limit = 1000
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
        while start_ts < end_ts:
            klines = self.client.futures_klines(
                symbol=symbol,
                interval=interval,
                startTime=start_ts,
                endTime=end_ts,
                limit=limit
            )
            if not klines:
                break
            all_klines.extend(klines)
            last_open_time = klines[-1][0]
            start_ts = last_open_time + 1
            time.sleep(0.3)
        # TODO: add candle data save
        columns = [
            'Open Time', 'Open', 'High', 'Low', 'Close', 'Volume',
            'Close Time', 'Quote Asset Volume', 'Number of Trades',
            'Taker Buy Base Asset Volume', 'Taker Buy Quote Asset Volume', 'Ignore'
        ]
        df = pd.DataFrame(all_klines, columns=columns)
        df['Open Time'] = pd.to_datetime(df['Open Time'], unit='ms')
        df['Close Time'] = pd.to_datetime(df['Close Time'], unit='ms')
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            df[col] = df[col].astype(float)
        return df

    def _enter_position(self, position_type, entry_price, amount):
        commission = amount * entry_price * self.commission_rate
        total_cost = amount * entry_price + commission
        if self.capital >= total_cost:
            if position_type == 'long':
                new_balance = amount
                new_capital = self.capital - total_cost
            else:
                new_balance = -amount
                new_capital = self.capital + amount * entry_price - commission
            self.pyramid_entry_commissions = [commission]
            return new_capital, new_balance
        else:
            return self.capital, 0

    def _exit_position(self, position_type, exit_price):
        bal = self.balance if self.balance is not None else 0.0
        entries = self.pyramid_entries if self.pyramid_entries is not None else []
        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []
        entry_commissions = self.pyramid_entry_commissions if hasattr(self, 'pyramid_entry_commissions') else []
        exit_commissions = []
        per_entry_profits = []
        self.exit_price = exit_price
        if position_type == 'long':
            new_capital = self.capital + bal * exit_price
            for ep, am in zip(entries, amounts):
                exit_comm = am * exit_price * self.commission_rate
                per_entry_profits.append((exit_price - ep) * am)
                exit_commissions.append(exit_comm)
            realized_profit = sum(per_entry_profits)
            max_profit = sum([(self.max_price_during_trade - ep) * am for ep, am in zip(entries, amounts)])
        else:
            new_capital = self.capital - abs(bal) * exit_price
            for ep, am in zip(entries, amounts):
                exit_comm = am * exit_price * self.commission_rate
                per_entry_profits.append((ep - exit_price) * am)
                exit_commissions.append(exit_comm)
            realized_profit = sum(per_entry_profits)
            max_profit = sum([(ep - self.min_price_during_trade) * am for ep, am in zip(entries, amounts)])
        self.pyramid_exit_commissions = exit_commissions
        total_entry_comm = sum(entry_commissions)
        total_exit_comm = sum(exit_commissions)
        new_capital = new_capital - total_entry_comm - total_exit_comm
        return new_capital, realized_profit, max_profit

    def _add_pyramid_position(self, position_type, pyramid_trigger, amount):
        cap = self.capital if self.capital is not None else 0.0
        entries = self.pyramid_entries if self.pyramid_entries is not None else []
        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []
        commission = amount * pyramid_trigger * self.commission_rate
        total_cost = amount * pyramid_trigger + commission
        if cap >= total_cost:
            if position_type == 'long':
                new_balance = amount
                new_capital = cap - total_cost
            else:
                new_balance = -amount
                new_capital = cap + amount * pyramid_trigger - commission
            new_pyramid_entries = entries + [pyramid_trigger]
            new_pyramid_amounts = amounts + [amount]
            if hasattr(self, 'pyramid_entry_commissions') and self.pyramid_entry_commissions:
                new_pyramid_entry_commissions = self.pyramid_entry_commissions + [commission]
            else:
                new_pyramid_entry_commissions = [commission]
            self.pyramid_entry_commissions = new_pyramid_entry_commissions
            return new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts
        else:
            return cap, 0, 0, entries, amounts

    def _check_stop_loss(self, position_type, entry_price, entry_atr, stop_mult, current_price):
        if position_type == 'long':
            stop_price = entry_price - stop_mult * entry_atr
            stop_triggered = current_price < stop_price
        else:
            stop_price = entry_price + stop_mult * entry_atr
            stop_triggered = current_price > stop_price
        return stop_triggered, stop_price

    def _check_exit_conditions(self, position_type, turtle_price, supertrend_price):
        if position_type == 'long':
            return turtle_price if turtle_price > supertrend_price else supertrend_price
        else:
            return turtle_price if turtle_price < supertrend_price else supertrend_price

    def _check_pyramid_conditions(self, position_type, current_price, prev_entry_price, entry_atr):
        if position_type == 'long':
            pyramid_trigger_price = prev_entry_price + 0.5 * entry_atr
            pyramid_triggered = current_price > pyramid_trigger_price
        else:
            pyramid_trigger_price = prev_entry_price - 0.5 * entry_atr
            pyramid_triggered = current_price < pyramid_trigger_price
        return pyramid_triggered, pyramid_trigger_price

    def _execute_pyramid_entry(self, position_type, current_price, prev_entry_price, entry_atr, amount):
        pyr = self.pyramid if self.pyramid is not None else 0
        max_pyr = self.max_pyramid if self.max_pyramid is not None else 0
        cap = self.capital if self.capital is not None else 0.0
        entries = self.pyramid_entries if self.pyramid_entries is not None else []
        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []
        if pyr >= max_pyr:
            return cap, 0, 0, entries, amounts, pyr, prev_entry_price, False
        pyramid_triggered, pyramid_trigger_price = self._check_pyramid_conditions(
            position_type, current_price, prev_entry_price, entry_atr
        )
        if pyramid_triggered:
            new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts = \
                self._add_pyramid_position(position_type, pyramid_trigger_price, amount)
            if new_balance != 0:
                new_pyramid = pyr + 1
                new_entry_price = pyramid_trigger_price
                return new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, True
            else:
                return cap, 0, entries, amounts, pyr, prev_entry_price, False
        else:
            return cap, 0, entries, amounts, pyr, prev_entry_price, False

    def run_backtest(self):
        for coin in self.COINS:
            params = self.PARITE_PARAMETRELERI[coin]
            for interval in params['INTERVALS']:
                for atr_window in params['ATR_WINDOWS']:
                    for atr_mult in params['ATR_MULTIPLIERS']:
                        for highlow_window in params['HIGHLOW_WINDOWS']:
                            for atr_mult_st in params['ATR_MULTIPLIERS_ST']:
                                for n_ratio_lower, n_ratio_upper in params['N_RATIOS']:
                                    for max_profit_limit, threshold_value, profit_follow_distance in params[
                                        'EXIT_PARAMS']:
                                        try:
                                            symbol = f"{coin}USDT"
                                            print(
                                                f"Başlıyor: {symbol} {interval} ATRwin:{atr_window} ATRmult:{atr_mult} HLwin:{highlow_window} PROFIT_FOLLOW_DISTANCE:{profit_follow_distance}")

                                            time.sleep(2)
                                            df = self.fetch_klines(symbol, interval, self.start_dt, self.end_dt)
                                            # ...devamı aynı...
                                            df['Previous Close'] = df['Close'].shift(1)
                                            df['TR'] = df[['High', 'Low', 'Previous Close']].apply(
                                                lambda row: max(
                                                    row['High'] - row['Low'],
                                                    abs(row['High'] - row['Previous Close']),
                                                    abs(row['Low'] - row['Previous Close'])
                                                ), axis=1
                                            )
                                            df[f'ATR_EMA{atr_window}'] = df['TR'].shift(1).ewm(span=atr_window,
                                                                                               adjust=False).mean()
                                            df[f'Low_{highlow_window}h'] = df['Low'].shift(1).rolling(
                                                window=highlow_window).min()
                                            df[f'High_{highlow_window}h'] = df['High'].shift(1).rolling(
                                                window=highlow_window).max()
                                            exit_window = int(highlow_window / 2)
                                            df[f'Low_{exit_window}h'] = df['Low'].shift(1).rolling(
                                                window=exit_window).min()
                                            df[f'High_{exit_window}h'] = df['High'].shift(1).rolling(
                                                window=exit_window).max()
                                            df = self.calc_supertrend(df, atr_window, atr_mult_st)
                                            df = self.calc_turtle_state(df, donchian_period=highlow_window)
                                            self._run_trading_simulation(
                                                df, coin, interval, atr_window, atr_mult,
                                                atr_mult_st, highlow_window, exit_window,
                                                n_ratio_lower, n_ratio_upper, max_profit_limit, threshold_value,
                                                profit_follow_distance
                                            )
                                        except Exception as e:
                                            traceback.print_exc()
                                            print(
                                                f"HATA: {coin} {interval} {atr_window} {atr_mult} {atr_mult_st} {highlow_window} -> {e}")
                                            continue
        self._process_final_results()

    def add_max_column(self, df):
        # Yeni kolon: max_cum_profit_drawdown
        max_cum = None
        freeze = False
        max_cum_list = []
        for val in df['cumulative_profit']:
            if max_cum is None or val > max_cum:
                max_cum = val
                freeze = False
                max_cum_list.append(0)
            elif val < max_cum:
                freeze = True
                max_cum_list.append(max_cum - val)
            else:
                max_cum_list.append(0)
        return max_cum_list

    def reset(self, new_capital):
        self.entry_price = None
        self.entry_atr = None
        self.pyramid = 0
        self.pyramid_entries = []
        self.pyramid_amounts = []
        self.pyramid_entry_commissions = []
        self.pyramid_exit_commissions = []
        self.max_price_during_trade = None
        self.min_price_during_trade = None
        self.capital = new_capital
        self.balance = 0
        self.state = 'neutral'
        self.first_entry_price = None  # Pozisyon kapandı, sıfırla

    def update_commissions_profit(self, i, realized_profit, max_profit):
        entry_index = i - self.how_many_hours[i]
        self.entry_commissions[entry_index] = sum(self.pyramid_entry_commissions)
        self.exit_commissions[entry_index] = sum(self.pyramid_exit_commissions)
        self.cumulative_profits[entry_index] = realized_profit - sum(
            self.pyramid_entry_commissions) - sum(
            self.exit_commissions)
        self.trade_profits[entry_index] = realized_profit
        self.max_possible_profits[entry_index] = max_profit

    def _run_trading_simulation(self, df, coin, interval, atr_window, atr_mult, atr_mult_st,
                                highlow_window, exit_window, n_ratio_lower, n_ratio_upper,
                                max_profit_limit, threshold_value, profit_follow_distance):
        initial_capital = 100000000
        self.capital = initial_capital
        self.balance = 0.0
        self.state = 'neutral'
        self.entry_price = None
        self.entry_atr = None
        self.pyramid = 0
        self.n_ratio_list = []
        self.max_price_during_trade = None
        self.min_price_during_trade = None
        self.cikis_sebebi = None
        self.block_reason = None
        self.states = []
        self.capitals = []
        self.balances = []
        self.amounts = []
        self.entry_prices = []
        self.pyramids = []
        self.exit_reasons = []
        self.exit_prices = []
        self.entry_block_reasons = []
        self.profits = []
        self.cumulative_profits = []
        self.commissions = []
        self.trade_profits = []
        self.max_possible_profits = []
        self.pyramid_entry_commissions = []
        self.pyramid_exit_commissions = []
        self.entry_commissions = []
        self.exit_commissions = []
        self.balance = 0.0
        self.pyramid_entries = []
        self.pyramid_amounts = []
        self.how_many_hours = []
        self.first_entry_price = None  # EKLENDİ: İlk piramit giriş fiyatı

        for i, row in df.iterrows():
            high = row['High']
            low = row['Low']
            atr = row[f'ATR_EMA{atr_window}']
            high_exit = row.get(f'High_{exit_window}h', np.nan)
            low_exit = row.get(f'Low_{exit_window}h', np.nan)
            st_up = row.get('supertrend_upperband', np.nan)
            st_low = row.get('supertrend_lowerband', np.nan)
            if self.state in ['long', 'short']:
                entries = self.pyramid_entries if self.pyramid_entries else []
                amounts = self.pyramid_amounts if self.pyramid_amounts else []
                if self.state == 'long':
                    self.max_price_during_trade = max(self.max_price_during_trade,
                                                      row['High']) if self.max_price_during_trade is not None else row[
                        'High']
                    current_max_profit = sum(
                        [(self.max_price_during_trade - ep) * am for ep, am in zip(entries, amounts)])
                else:
                    self.min_price_during_trade = min(self.min_price_during_trade,
                                                      row['Low']) if self.min_price_during_trade is not None else row[
                        'Low']
                    current_max_profit = sum(
                        [(ep - self.min_price_during_trade) * am for ep, am in zip(entries, amounts)])
                self.max_possible_profits[-1] = current_max_profit
            self.cikis_sebebi = None
            self.exit_price = None
            self.block_reason = None
            realized_profit = 0
            self.trade_profits.append(0)
            self.cumulative_profits.append(0)
            self.entry_commissions.append(0)
            self.exit_commissions.append(0)
            self.max_possible_profits.append(0)
            self.n_ratio_list.append(None)

            prev_state = self.state

            current_turtle_state = df['turtle_state'].iloc[i]
            current_supertrend_state = df['supertrend_state'].iloc[i]

            # n_ratio hesaplama ve sabitleme
            if prev_state == 'neutral':
                self.try_enter_position(row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper)
                self.how_many_hours.append(0)

            if prev_state == 'long':
                self.how_many_hours.append(self.how_many_hours[-1] + 1)
                if self.max_price_during_trade is not None:
                    self.max_price_during_trade = max(self.max_price_during_trade, high)
                if self.min_price_during_trade is not None:
                    self.min_price_during_trade = min(self.min_price_during_trade, low)
                realized_profit = sum(
                    [(row['Close'] - ep) * am for ep, am in zip(self.pyramid_entries, self.pyramid_amounts)])
                max_profit = sum([(self.max_price_during_trade - ep) * am for ep, am in
                                  zip(self.pyramid_entries, self.pyramid_amounts)])

                if max_profit < max_profit_limit:
                    threshold = threshold_value
                else:
                    threshold = max_profit - profit_follow_distance

                # Çıkış kontrolü
                if realized_profit <= threshold:
                    total_amount = sum(self.pyramid_amounts)
                    avg_entry = sum([ep * am for ep, am in zip(self.pyramid_entries,
                                                               self.pyramid_amounts)]) / total_amount if total_amount > 0 else self.entry_price
                    exit_price = avg_entry + (threshold / total_amount)
                    new_capital, realized_profit, max_profit = self._exit_position('long', exit_price)
                    self.update_commissions_profit(i, realized_profit, max_profit)
                    self.max_possible_profits[-1] = max_profit
                    self.cikis_sebebi = f'Kar Eşiği Altı Çıkış ({threshold})'
                    self.reset(new_capital)
                    # self.try_enter_position(row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper)
                elif current_turtle_state in ['short', 'neutral'] and current_supertrend_state == 'short':
                    final_exit_price = self._check_exit_conditions('long', low_exit, st_low)
                    if final_exit_price:
                        new_capital, realized_profit, max_profit = self._exit_position(
                            'long', final_exit_price
                        )
                        self.update_commissions_profit(i, realized_profit, max_profit)
                        self.max_possible_profits[-1] = max_profit  # Sadece kapanışta güncelle
                        self.reset(new_capital)
                        self.cikis_sebebi = f'Long Low{exit_window} Çıkışı'
                        self.try_enter_position(
                            row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper
                        )
                    else:
                        self.cikis_sebebi = 'Pozisyon Açık'
                elif self.pyramid < self.max_pyramid:
                    piramit_yapildi = False
                    # Ekle: Bard başındaki piramit listesini sakla
                    base_entries = self.pyramid_entries.copy()
                    base_amounts = self.pyramid_amounts.copy()
                    base_commissions = self.pyramid_entry_commissions.copy()
                    base_pyramid = self.pyramid
                    base_entry_price = self.entry_price
                    while self.pyramid < self.max_pyramid:
                        new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \
                            self._execute_pyramid_entry('long', high, self.entry_price, self.entry_atr, self.amount)
                        if pyramid_executed:
                            self.capital = new_capital
                            self.balance += new_balance
                            # Sadece bir tane amount ekle!
                            self.pyramid_entries = base_entries + [new_entry_price] * (new_pyramid - base_pyramid)
                            self.pyramid_amounts = base_amounts + [self.amount] * (new_pyramid - base_pyramid)
                            self.pyramid = new_pyramid
                            self.entry_price = new_entry_price
                            piramit_yapildi = True
                        else:
                            break
                    self.cikis_sebebi = 'Long Pyramid' if piramit_yapildi else 'Pozisyon Açık'

            elif prev_state == 'short':
                self.how_many_hours.append(self.how_many_hours[-1] + 1)
                if self.max_price_during_trade is not None:
                    self.max_price_during_trade = max(self.max_price_during_trade, high)
                if self.min_price_during_trade is not None:
                    self.min_price_during_trade = min(self.min_price_during_trade, low)
                realized_profit = sum(
                    [(ep - row['Close']) * am for ep, am in zip(self.pyramid_entries, self.pyramid_amounts)])
                # max_profit her adımda güncellenmeli!
                max_profit = sum([(ep - self.min_price_during_trade) * am for ep, am in
                                  zip(self.pyramid_entries, self.pyramid_amounts)])
                # Yeni threshold kuralı
                if max_profit < max_profit_limit:
                    threshold = threshold_value
                else:
                    threshold = max_profit - profit_follow_distance

                # Çıkış kontrolü
                if realized_profit <= threshold:
                    total_amount = sum(self.pyramid_amounts)
                    avg_entry = sum([ep * am for ep, am in zip(self.pyramid_entries,
                                                               self.pyramid_amounts)]) / total_amount if total_amount > 0 else self.entry_price
                    exit_price = avg_entry - (threshold / total_amount)
                    new_capital, realized_profit, max_profit = self._exit_position('short', exit_price)
                    self.update_commissions_profit(i, realized_profit, max_profit)
                    self.max_possible_profits[-1] = max_profit
                    self.cikis_sebebi = f'Kar Eşiği Altı Çıkış ({threshold})'
                    self.reset(new_capital)
                    # self.try_enter_position(row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper)

                elif (not np.isnan(high_exit) and high > high_exit) and current_supertrend_state == 'long':
                    final_exit_price = self._check_exit_conditions('short', high_exit, st_up)
                    if final_exit_price:
                        new_capital, realized_profit, max_profit = self._exit_position(
                            'short', final_exit_price
                        )
                        self.update_commissions_profit(i, realized_profit, max_profit)
                        self.max_possible_profits[-1] = max_profit  # Sadece kapanışta güncel
                        self.reset(new_capital)
                        self.cikis_sebebi = f'Short High{exit_window} Çıkışı'
                        self.try_enter_position(
                            row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper
                        )
                        current_n_ratio = None
                    else:
                        self.cikis_sebebi = 'Pozisyon Açık'
                elif self.pyramid < self.max_pyramid:
                    piramit_yapildi = False
                    # Ekle: Bard başındaki piramit listesini sakla
                    base_entries = self.pyramid_entries.copy()
                    base_amounts = self.pyramid_amounts.copy()
                    base_commissions = self.pyramid_entry_commissions.copy()
                    base_pyramid = self.pyramid
                    base_entry_price = self.entry_price
                    while self.pyramid < self.max_pyramid:
                        new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \
                            self._execute_pyramid_entry('short', low, self.entry_price, self.entry_atr, self.amount)
                        if pyramid_executed:
                            self.capital = new_capital
                            self.balance += new_balance
                            # Sadece bir tane amount ekle!
                            self.pyramid_entries = base_entries + [new_entry_price] * (new_pyramid - base_pyramid)
                            self.pyramid_amounts = base_amounts + [self.amount] * (new_pyramid - base_pyramid)
                            self.pyramid = new_pyramid
                            self.entry_price = new_entry_price
                            piramit_yapildi = True
                        else:
                            break
                    self.cikis_sebebi = 'Short Pyramid' if piramit_yapildi else 'Pozisyon Açık'

            self.profits.append(realized_profit)
            self.exit_prices.append(self.exit_price)
            self.entry_block_reasons.append(self.block_reason)
            self.states.append(self.state)
            self.capitals.append(self.capital)
            self.balances.append(self.balance)
            if self.state in ['long', 'short'] and self.pyramid_amounts:
                amount2 = sum(self.pyramid_amounts)
            else:
                amount2 = 0
            self.amounts.append(amount2)
            self.entry_prices.append(self.entry_price)
            self.pyramids.append(self.pyramid)
            self.exit_reasons.append(self.cikis_sebebi)

        df['State'] = self.states
        df['capital'] = self.capitals
        df['balance'] = self.balances
        df['amount'] = self.amounts
        df['entry_price'] = self.entry_prices
        df['pyramid'] = self.pyramids
        df['exit_reason'] = self.exit_reasons
        df['exit_price'] = self.exit_prices
        df['commissions'] = [a + b for a, b in zip(self.entry_commissions, self.exit_commissions)]
        df['entry_block_reason'] = self.entry_block_reasons
        df['profit'] = self.trade_profits
        df['entry_commissions'] = self.entry_commissions
        df['exit_commissions'] = self.exit_commissions
        df['profit_comission_included'] = df['profit'] - df['entry_commissions'] - df['exit_commissions']
        df['cumulative_profit'] = df['profit_comission_included'].cumsum()
        df['trade_profit'] = self.trade_profits
        df['max_possible_profit'] = self.max_possible_profits
        df['n_ratio'] = self.n_ratio_list
        df['max_cum_profit_drawdown'] = self.add_max_column(df)

        drop_cols = ['Volume', 'Close Time', 'Quote Asset Volume', 'Number of Trades', 'Taker Buy Base Asset Volume',
                     'Taker Buy Quote Asset Volume', 'Ignore', 'Previous Close', 'capital', 'balance',
                     'entry_block_reason', 'trade_profit']
        for col in drop_cols:
            if col in df.columns:
                df.drop(col, axis=1, inplace=True)

        print("\nDataFrame Structure:")
        print(df.info())
        print("\nFirst 5 rows of DataFrame:")
        print(df.head())
        print("\nTurtle State Analysis:")
        print(df[['Open Time', 'Close', 'donchian_high', 'donchian_low', 'turtle_state', 'supertrend_state']].tail(10))
        print("\nTurtle State Distribution:")
        print(df['turtle_state'].value_counts())
        print("\nComparison with Supertrend:")
        state_comparison = pd.crosstab(df['turtle_state'], df['supertrend_state'])
        print(state_comparison)

        excel_file = f"{coin}_{interval}_ATRW{atr_window}_ATRm{atr_mult}_ATRst{atr_mult_st}_HLW{highlow_window}_PROFITDIST{profit_follow_distance}_MAXPROFIT{max_profit_limit}_THRESHOLD{threshold_value}.xlsx"
        with pd.ExcelWriter(excel_file, engine="xlsxwriter") as writer:
            df.to_excel(writer, index=False)
            worksheet = writer.sheets["Sheet1"]
            worksheet.freeze_panes(1, 1)
        print(f"Kaydedildi: {excel_file}")

        df['year'] = df['Open Time'].dt.year
        yearly_trade_profits = df.groupby('year')['cumulative_profit'].sum().to_dict()
        profit_2022 = yearly_trade_profits.get(2022, 0)
        profit_2023 = yearly_trade_profits.get(2023, 0)
        profit_2024 = yearly_trade_profits.get(2024, 0)
        profit_2025 = yearly_trade_profits.get(2025, 0)

        last_cum_profit = df['cumulative_profit'].iloc[-1] if 'cumulative_profit' in df.columns else 0
        trade_count = sum([1 for k in self.commissions if k > 0])
        total_commission = sum(self.commissions)
        trade_volume = total_commission / self.commission_rate if self.commission_rate > 0 else 0
        self.results.append({
            "COIN": coin,
            "INTERVAL": interval,
            "ATR_WINDOW": atr_window,
            "ATR_MULT": atr_mult,
            "ATR_MULT_ST": atr_mult_st,
            "HIGHLOW_WINDOW": highlow_window,
            "PROFIT_2022": profit_2022,
            "PROFIT_2023": profit_2023,
            "PROFIT_2024": profit_2024,
            "PROFIT_2025": profit_2025,
            "CUMULATIVE_PROFIT": last_cum_profit,
            "TRADE_COUNT": trade_count,
            "TOTAL_COMMISSION": total_commission,
            "TRADE_VOLUME": trade_volume,
            "MAX_PROFIT_LIMIT": max_profit_limit,
            "PROFIT_FOLLOW_DISTANCE": profit_follow_distance,
            "THRESHOLD_STOP": threshold_value,
        })

    def _process_final_results(self):
        import pandas as pd

        summary_rows = []
        monthly_rows = []
        drawdown_zero_rows = []
        consecutive_false_summary = {}

        for result in self.results:
            excel_file = f"{result['COIN']}_{result['INTERVAL']}_ATRW{result['ATR_WINDOW']}_ATRm{result['ATR_MULT']}_ATRst{result['ATR_MULT_ST']}_STOPm{result['STOP_MULT']}_HLW{result['HIGHLOW_WINDOW']}_PROFITDIST{result.get('PROFIT_FOLLOW_DISTANCE', '')}.xlsx"
            try:
                df = pd.read_excel(excel_file)

                df['year_month'] = df['Open Time'].dt.to_period('M')
                monthly_profit = df.groupby('year_month')['profit_comission_included'].sum()
                yearly_profit = df.groupby(df['Open Time'].dt.year)['profit_comission_included'].sum()
                max_drawdown_last = df['max_cum_profit_drawdown'].iloc[-1]
                max_drawdown_max = df['max_cum_profit_drawdown'].max()

                drawdown_months = df.groupby('year_month')['max_cum_profit_drawdown'].apply(lambda x: (x == 0).any())
                false_streak = 0
                max_false_streak = 0
                for is_zero in drawdown_months.values:
                    if not is_zero:
                        false_streak += 1
                        if false_streak > max_false_streak:
                            max_false_streak = false_streak
                    else:
                        false_streak = 0
                consecutive_false_summary[result['COIN']] = max_false_streak

                for ym, is_zero in drawdown_months.items():
                    drawdown_zero_rows.append({
                        "Coin": result['COIN'],
                        "Year-Month": str(ym),
                        "Drawdown Zero": is_zero
                    })

                for ym, val in monthly_profit.items():
                    monthly_rows.append({
                        "Coin": result['COIN'],
                        "Year-Month": str(ym),
                        "Monthly Profit": val
                    })
                monthly_rows.append({
                    "Coin": result['COIN'],
                    "Year-Month": "Ortalama",
                    "Monthly Profit": monthly_profit.mean()
                })

                summary_rows.append({
                    "Coin": result['COIN'],
                    "ATR Multiplier": result['ATR_MULT'],
                    "Supertrend ATR": result['ATR_MULT_ST'],
                    "Cumulative Trade Profit (comission included)": df['profit_comission_included'].sum(),
                    "Total Commission": df['commissions'].sum(),
                    "Monthly Avg Profit": monthly_profit.mean(),
                    "Yearly Avg Profit": yearly_profit.mean(),
                    "Max Drawdown Last": max_drawdown_last,
                    "Max Drawdown Max": max_drawdown_max,
                    "Max Consecutive Drawdown Zero False": max_false_streak,
                    "ATR_WINDOW": result['ATR_WINDOW'],
                    "STOP_MULT": result['STOP_MULT'],
                    "HIGHLOW_WINDOW": result['HIGHLOW_WINDOW'],
                    "INTERVAL": result['INTERVAL'],
                    "MAX_PROFIT_LIMIT": result.get('MAX_PROFIT_LIMIT', None),  # <-- EKLENDİ
                    "PROFIT_FOLLOW_DISTANCE": result.get('PROFIT_FOLLOW_DISTANCE', None),  # <-- EKLENDİ
                })

            except Exception as e:
                print(f"Tablo oluşturulamadı: {excel_file} - {e}")
                continue

        summary_df = pd.DataFrame(summary_rows)
        monthly_df = pd.DataFrame(monthly_rows)
        drawdown_zero_df = pd.DataFrame(drawdown_zero_rows)

        # Sıralama: Monthly Avg Profit'e göre büyükten küçüğe
        summary_df = summary_df.sort_values(by="Monthly Avg Profit", ascending=False)

        # Excel'e yaz ve biçimlendir
        with pd.ExcelWriter("tum_sonuclar_sirali_1.xlsx", engine="xlsxwriter") as writer:
            summary_df.to_excel(writer, sheet_name="OzetTablo", index=False)
            monthly_df.to_excel(writer, sheet_name="AylikKarZarar", index=False)
            drawdown_zero_df.to_excel(writer, sheet_name="DrawdownSifirAylar", index=False)

            workbook = writer.book
            worksheet = writer.sheets["OzetTablo"]

            # Tüm tabloya border ekle
            border_format = workbook.add_format({'border': 1})

            # Alternatif satır renkleri
            color1 = workbook.add_format({'bg_color': '#F2F2F2', 'border': 1})
            color2 = workbook.add_format({'bg_color': '#DDEBF7', 'border': 1})

            # Başlıkları bold yap
            header_format = workbook.add_format({'bold': True, 'border': 1, 'bg_color': '#B7DEE8'})
            for col_num, value in enumerate(summary_df.columns.values):
                worksheet.write(0, col_num, value, header_format)

            # Coin adlarını bold yap
            bold_format = workbook.add_format({'bold': True, 'border': 1})

            # Satırları yaz ve biçimlendir
            for row_num, row_data in enumerate(summary_df.values, start=1):
                fmt = color1 if row_num % 2 == 0 else color2
                for col_num, cell_data in enumerate(row_data):
                    if col_num == 0:  # Coin adı sütunu
                        worksheet.write(row_num, col_num, cell_data, bold_format)
                    else:
                        worksheet.write(row_num, col_num, cell_data, fmt)

            # DrawdownSifirAylar tablosunun en altına summary ekle
            worksheet_dd = writer.sheets["DrawdownSifirAylar"]
            last_row = len(drawdown_zero_df) + 2
            worksheet_dd.write(last_row, 0, "Max Consecutive Drawdown Zero False Summary")
            for idx, (coin, streak) in enumerate(consecutive_false_summary.items()):
                worksheet_dd.write(last_row + idx + 1, 0, coin, bold_format)
                worksheet_dd.write(last_row + idx + 1, 1, streak, border_format)

        print("Analiz tabloları 'tum_sonuclar_sirali_1.xlsx' dosyasına kaydedildi.")

        # If no results, print a message
        if not self.results:
            print("Hiç sonuç bulunamadı.")

    def try_enter_position(self, row, atr_mult, atr_window, highlow_window, df, i, n_ratio_upper, n_ratio_lower):
        high = row['High']
        low = row['Low']
        atr = row[f'ATR_EMA{atr_window}']
        high_hl = row[f'High_{highlow_window}h']
        low_hl = row[f'Low_{highlow_window}h']
        st_up = row.get('supertrend_upperband', None)
        st_low = row.get('supertrend_lowerband', None)
        turtle_switch, supertrend_switch = self.check_indicator_switches(df, i)
        current_turtle_state = df['turtle_state'].iloc[i]
        current_supertrend_state = df['supertrend_state'].iloc[i]
        self.block_reason = None
        current_atr = atr

        position_type = None
        entry_price = 1

        if turtle_switch and supertrend_switch:
            if current_turtle_state == 'long' and current_supertrend_state == 'long':
                position_type = 'long'
                entry_price = high_hl if high_hl > st_up else st_up
            elif current_turtle_state == 'short' and current_supertrend_state == 'short':
                position_type = 'short'
                entry_price = low_hl if low_hl < st_low else st_low
        elif turtle_switch or supertrend_switch:
            if current_turtle_state == 'long' and current_supertrend_state == 'long':
                position_type = 'long'
                entry_price = high_hl if turtle_switch and not supertrend_switch else st_up
            elif current_turtle_state == 'short' and current_supertrend_state == 'short':
                position_type = 'short'
                entry_price = low_hl if turtle_switch and not supertrend_switch else st_low
        elif (current_turtle_state == 'long' and current_supertrend_state == 'long') or \
                (current_turtle_state == 'short' and current_supertrend_state == 'short'):
            if current_turtle_state == 'long' and high > high_hl:
                position_type = 'long'
                entry_price = high_hl
            elif current_turtle_state == 'short' and low < low_hl:
                position_type = 'short'
                entry_price = low_hl
            else:
                self.block_reason = 'Fiyat koşulları sağlanmadı' if position_type is None else 'Fiyat yeterli degil'

        atr = 0.004 * entry_price  # ATR'yi N Ratio'ya sabitle
        self.amount = 0 if (atr is None or np.isnan(atr) or atr == 0) else 20 / (atr_mult * atr)
        self.calc_n_ratio = atr / entry_price

        if n_ratio_upper < self.calc_n_ratio or n_ratio_lower > self.calc_n_ratio:
            self.block_reason = 'N Ratio'

        elif position_type and entry_price:
            new_capital, new_balance = self._enter_position(
                position_type, entry_price, self.amount
            )
            if new_balance != 0:
                self.state = position_type
                self.entry_price = entry_price
                if self.first_entry_price is None:
                    self.first_entry_price = entry_price

                if self.first_entry_price is None:
                    self.first_entry_price = entry_price

                if self.state != 'neutral' and self.first_entry_price:
                    current_n_ratio = atr / self.first_entry_price
                else:
                    current_n_ratio = None
                self.n_ratio_list[i] = current_n_ratio
                self.how_many_hours[-1] = 0  # reset counter for instant switch position within the same hour

                self.entry_atr = atr
                self.pyramid = 1
                self.capital = new_capital
                self.balance = new_balance
                self.pyramid_entries = [entry_price]
                self.pyramid_amounts = [self.amount]
                self.max_price_during_trade = row['High']
                self.min_price_during_trade = row['Low']
                self.cikis_sebebi = 'Açık Pozisyon'

                # PROFIT VE MAX_POSSIBLE_PROFIT İŞLEM AÇILIŞINDA YAZILIYOR
                self.trade_profits[i] = 0  # Açılışta profit 0
                self.max_possible_profits[i] = 0  # Açılışta max_possible_profit 0

                while self.pyramid < self.max_pyramid:
                    new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \
                        self._execute_pyramid_entry(
                            position_type, high if position_type == 'long' else low, self.entry_price, self.entry_atr,
                            self.amount
                        )
                    if pyramid_executed:
                        self.capital = new_capital
                        self.balance += new_balance
                        self.pyramid_entries = new_pyramid_entries
                        self.pyramid_amounts = new_pyramid_amounts
                        self.pyramid = new_pyramid
                        self.entry_price = new_entry_price
                        self.cikis_sebebi = f'{position_type} Pyramid'
                    else:
                        break
            else:
                self.block_reason = 'Yetersiz sermaye'


# Main execution
if __name__ == "__main__":
    backtest = BacktestMeta()
    backtest.run_backtest()