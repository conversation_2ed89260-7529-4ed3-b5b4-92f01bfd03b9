import os
import pickle

from common.enums.decision import Decision
from common.enums.exit_reason import ExitReason
from common.enums.position import Position
from common.enums.time_interval import TimeInterval
from logic.data_containers.position_reporter import PositionReporter
from logic.data_containers.trader_parameters import TraderParameters
from logic.data_containers.trader_state import TradeState
from logic.signal_mixer import SignalMixer
from logic.signals.archelon import Archelon
from logic.signals.atr_exit_signal import AtrExitSignal
from logic.signals.supertrend_signal import SuperTrendSignal


class SuperArchelon:
    interval_map = {
        "1m": TimeInterval.MINUTE,
        "5m": TimeInterval.FIVE_MIN,
        "15m": TimeInterval.FIFTEEN_MIN,
        "1h": TimeInterval.ONE_HOUR,
        "4h": TimeInterval.FOUR_HOUR
    }

    def __init__(self, token, logger, exchange, trade_params: TraderParameters):
        # vitals
        self.token = token
        self.logger = logger
        self.signal_mixer = SignalMixer(logger)
        self.exchange = exchange

        self.trade_state = TradeState()
        self.trade_params = trade_params
        self.archelon = Archelon(logger, trade_params)
        self.super_trend = SuperTrendSignal(trade_params, self.logger)
        self.atr_exit_signal = AtrExitSignal(trade_params)
        # signals
        self.position_reporter = PositionReporter()

        self.current_second_candles = None
        self.current_second_index = 0
        self.max_profit =self.trade_params.basic_stop

    def record_max_profit(self):
        if self.trade_state.current_profit > self.trade_state.max_profit:
            self.trade_state.max_profit = self.trade_state.current_profit

    def calculate_profit_traliling_stop(self):
        atr_exit_decision = Decision.do_nothing
        if self.trade_state.current_active_position != Position.notr:
            position_value = self.trade_state.current_active_position.value
            pyramid_count = self.archelon.archelon_pyramid.pyramid_count

            # Calculate average entry price based on actual executed prices
            if pyramid_count == 0:
                # No pyramid entries yet, use the initial entry price
                avg_entry_price = self.trade_state.entry_price
            else:
                # Calculate average of all executed pyramid entries (including initial entry)
                total_entries = pyramid_count + 1  # +1 for the initial entry
                if len(self.archelon.archelon_pyramid.pyramid_entries) >= total_entries:
                    # Sum all executed entry prices
                    total_price = sum(self.archelon.archelon_pyramid.pyramid_entries[:total_entries])
                    avg_entry_price = round(total_price / total_entries, 5)
                else:
                    # Fallback to initial entry price if pyramid entries are not properly set
                    self.logger.warning(f"Pyramid entries array length ({len(self.archelon.archelon_pyramid.pyramid_entries)}) "
                                      f"is less than expected total entries ({total_entries}). Using initial entry price.")
                    avg_entry_price = self.trade_state.entry_price

            self.trade_state.current_profit = position_value * (self.trade_state.current_price - avg_entry_price) * self.trade_state.amount * (pyramid_count+1)

            self.record_max_profit()
            if self.trade_state.max_profit > self.trade_params.trailing_limit:
                self.trade_params.basic_stop = self.trade_state.max_profit - self.trade_params.trailing_distance
                self.logger.info(f"Max profit: {self.trade_state.max_profit}, "
                                 f"Basic stop: {self.trade_params.basic_stop}")
            if self.trade_state.current_profit <= self.trade_params.basic_stop:
                atr_exit_decision = Decision.market_exit
                self.logger.info(f"Trailing stop triggered - Current profit: {self.trade_state.current_profit}, "
                                 f"Max profit: {self.trade_state.max_profit}, Basic stop: {self.trade_params.basic_stop}")
        return atr_exit_decision


    def process_decisions(self):

        self.trade_state.last_atr_exit_decision = self.calculate_profit_traliling_stop()

        archelon_decision = self.archelon.run_signal(self.trade_state, self.candle_data)
        if archelon_decision in [Decision.pyramid, Decision.market_exit, Decision.pyramid_exit]:
            self.trade_state.archelon_state.update_state(archelon_decision)

        super_trend_decision = self.super_trend.run_signal(self.candle_data, self.trade_state, self.trade_params)
        super_trend_decision = self.super_trend.check_wick(self.trade_state)
        self.trade_state.supertrend_state.update_state(super_trend_decision)

        if self.trade_state.current_active_position == Position.notr and super_trend_decision in [Decision.buy,
                                                                                                  Decision.sell]:
            if archelon_decision == super_trend_decision:
                self.trade_state.archelon_state.update_state(archelon_decision)
                entry_stop_loss = self.archelon.calculate_entry_stop_loss(
                    self.trade_state.current_price, archelon_decision, self.trade_params.atr_multiplier
                )
                self.archelon.archelon_pyramid.calculate_pyramid_stop_loss(
                    self.trade_state.current_price, entry_stop_loss, self.archelon.atr_value, archelon_decision
                )

        ultimate_decision = self.signal_mixer.combine_signals(self.trade_state)

        if ultimate_decision == Decision.buy and self.trade_params.position_preference == 'short_only':
            return ultimate_decision # kill switch for position preference
        elif ultimate_decision == Decision.sell and self.trade_params.position_preference == "long_only":
            return ultimate_decision

        if ultimate_decision != Decision.do_nothing:
            self.position_reporter.decision = ultimate_decision
            self.execute_decision(ultimate_decision)

        if archelon_decision == Decision.pyramid:
            self.logger.info(f"Pyramid decision: {archelon_decision}")
            self.trade_state.archelon_state.pyramid_decision = Decision.do_nothing

        if ultimate_decision != Decision.do_nothing:
            self.logger.info(f"Ultimate decision: {ultimate_decision}")
            self.logger.info("#" * 80)
        return ultimate_decision

    def interval_loop(self):
        self.update_trade_state(True)  # candle data init inside
        self.trade_state = self.archelon.init_archelon(self.trade_state, self.trade_params, self.candle_data)
        ultimate_decision = self.process_decisions()
        self.logger.info(f"cp: {self.trade_state.current_price}, atr_exit: {self.atr_exit_signal.atr_exit_value}, py: {self.archelon.archelon_pyramid.pyramid_entries}")

    def instant_loop(self):
        self.update_trade_state()
        ultimate_decision = self.process_decisions()

    def update_trade_state(self, fetch_candles: bool = False):
        if not fetch_candles:
            self.trade_state.current_price = self.exchange.get_current_futures_price(self.trade_params.token)
        else:
            candle_data = self.exchange.get_and_check_candle(self.trade_params.token, self.trade_params.interval)
            self.trade_state.current_price = candle_data["close"].iloc[-1]
            self.candle_data = candle_data.iloc[:-1]
            pass

    def get_order_entry_price(self, order_id: int, symbol: str) -> float:
        """
        Query the exchange to get the actual executed price of an order.

        Args:
            order_id: The order ID returned from order execution
            symbol: Trading pair symbol

        Returns:
            The average executed price of the order
        """
        try:
            # Query the order details to get the actual executed price
            order_details = self.exchange.client.futures_get_order(symbol=symbol, orderId=order_id)
            avg_price = float(order_details.get('avgPrice', 0))

            if avg_price > 0:
                self.logger.info(f"Order {order_id} executed at average price: {avg_price:.4f}")
                return avg_price
            else:
                self.logger.warning(f"Order {order_id} not yet filled or avgPrice is 0, using current price")
                return self.trade_state.current_price

        except Exception as e:
            self.logger.error(f"Error getting order entry price for order {order_id}: {e}")
            return self.trade_state.current_price

    def execute_decision(self, decision: Decision):
        if decision != Decision.do_nothing:
            archelon_amount = self.trade_state.amount
            symbol = self.trade_params.token

            # Process the decision logic here before calling the exchange
            if decision == Decision.pyramid:
                # Get current position information
                position = self.exchange.client.futures_position_information(symbol=symbol)
                if position and float(position[0]['positionAmt']) != 0:
                    # Store the current pyramid count before executing
                    current_pyramid_level = self.archelon.archelon_pyramid.pyramid_count

                    # Log the pyramid level we're trying to execute
                    next_entry = self.archelon.archelon_pyramid.get_pyramid_entry()
                    self.logger.info(
                        f"Attempting to execute pyramid level {current_pyramid_level + 1} for {symbol} at price {self.trade_state.current_price:.4f}, target entry: {next_entry:.4f}")

                    current_position_amount = float(position[0]['positionAmt'])
                    side = 'BUY' if current_position_amount > 0 else 'SELL'

                    # Execute the pyramid order on the exchange
                    result = self.exchange.execute_order(symbol, side, archelon_amount, self.trade_params.leverage)

                    if result is not None:
                        order_id = result.get('orderId')
                        if order_id:
                            # Get the actual executed price
                            actual_entry_price = self.get_order_entry_price(order_id, symbol)

                            pyramid_entry_index = current_pyramid_level + 1
                            self.archelon.archelon_pyramid.pyramid_entries[pyramid_entry_index] = actual_entry_price

                            self.logger.info(f"Pyramid level {current_pyramid_level + 1} order executed for {symbol} at actual price: {actual_entry_price:.4f}")
                            self.archelon.archelon_pyramid.increase_pyramid_count()
                        else:
                            self.logger.warning(f"No order ID returned from pyramid execution for {symbol}")
                else:
                    self.logger.warning(f"No position to pyramid for {symbol}")
                    return

            elif decision == Decision.buy:
                # Check if we already have a position
                if self.trade_state.current_active_position != Position.notr:
                    self.logger.info(f"Already in a {self.trade_state.current_active_position} position. Skipping BUY.")
                    return

                # Execute buy order
                self.logger.info(f"Executing BUY order for {symbol} at price {self.trade_state.current_price:.4f}")
                result = self.exchange.execute_order(symbol, 'BUY', archelon_amount, self.trade_params.leverage)

                if result is not None:
                    order_id = result.get('orderId')
                    if order_id:
                        # Get the actual executed price
                        actual_entry_price = self.get_order_entry_price(order_id, symbol)

                        # Set the entry price to the actual executed price
                        self.trade_state.entry_price = actual_entry_price

                        # Update the first pyramid entry with the actual executed price
                        if len(self.archelon.archelon_pyramid.pyramid_entries) > 0:
                            self.archelon.archelon_pyramid.pyramid_entries[0] = actual_entry_price

                        self.logger.info(f"BUY order executed for {symbol} at actual price {actual_entry_price:.4f}")
                        self.trade_state.current_active_position = Position.long
                        self.atr_exit_signal.initialize(self.archelon.atr_value, self.trade_state)
                    else:
                        self.logger.warning(f"No order ID returned from BUY execution for {symbol}")
                        # Fallback to current price
                        self.trade_state.entry_price = self.trade_state.current_price
                        self.trade_state.current_active_position = Position.long
                        self.atr_exit_signal.initialize(self.archelon.atr_value, self.trade_state)

            elif decision == Decision.sell:
                # Check if we already have a position
                if self.trade_state.current_active_position != Position.notr:
                    self.logger.info(
                        f"Already in a {self.trade_state.current_active_position} position. Skipping SELL.")
                    return

                self.logger.info(f"Executing SELL order for {symbol} at price {self.trade_state.current_price:.4f}")
                result = self.exchange.execute_order(symbol, 'SELL', archelon_amount, self.trade_params.leverage)

                if result is not None:
                    order_id = result.get('orderId')
                    if order_id:
                        # Get the actual executed price
                        actual_entry_price = self.get_order_entry_price(order_id, symbol)

                        # Set the entry price to the actual executed price
                        self.trade_state.entry_price = actual_entry_price

                        # Update the first pyramid entry with the actual executed price
                        if len(self.archelon.archelon_pyramid.pyramid_entries) > 0:
                            self.archelon.archelon_pyramid.pyramid_entries[0] = actual_entry_price

                        self.logger.info(f"SELL order executed for {symbol} at actual price {actual_entry_price:.4f}")
                        self.trade_state.current_active_position = Position.short
                        self.atr_exit_signal.initialize(self.archelon.atr_value, self.trade_state)
                    else:
                        self.logger.warning(f"No order ID returned from SELL execution for {symbol}")
                        # Fallback to current price
                        self.trade_state.entry_price = self.trade_state.current_price
                        self.trade_state.current_active_position = Position.short
                        self.atr_exit_signal.initialize(self.archelon.atr_value, self.trade_state)

            elif decision == Decision.market_exit:
                # Get current position information
                position = self.exchange.client.futures_position_information(symbol=symbol)

                if position and float(position[0]['positionAmt']) != 0:
                    # Determine side based on current position
                    position_amount = float(position[0]['positionAmt'])
                    side = 'SELL' if position_amount > 0 else 'BUY'
                    exit_amount = abs(position_amount)

                    # Execute market exit order
                    self.logger.info(
                        f"Executing market exit for {symbol} at price {self.trade_state.current_price:.4f}")
                    result = self.exchange.execute_order(
                        symbol,
                        side,
                        exit_amount,
                        self.trade_params.leverage,
                        reduce_only=True
                    )

                    if result is not None:
                        self.logger.info(f"Position closed for {symbol} at price {self.trade_state.current_price:.4f}")
                        self.archelon.archelon_pyramid.reset()
                        self.trade_state.reset()
                else:
                    self.logger.info(f"No position to exit for {symbol}")

    def save_trader(self):
        path = f"./config/traders/{self.token}_full.pkl"
        data = {
            'trade_params': self.trade_params,
            'trade_state': self.trade_state,
            'archelon': self.archelon,
            'super_trend': self.super_trend,
            'atr_exit_signal': self.atr_exit_signal
        }
        with open(path, "wb") as f:
            pickle.dump(data, f)
            self.logger.debug(f"Trader parameters and state saved to {path}")

    def load(self):
        path = f"./config/traders/{self.token}_full.pkl"
        if not os.path.exists(path):
            self.save_trader()
        try:
            with open(path, "rb") as f:
                data = pickle.load(f)
            self.trade_params = data['trade_params']
            self.trade_state = data['trade_state']
            self.archelon = data['archelon']
            self.super_trend = data['super_trend']
            self.atr_exit_signal = data['atr_exit_signal']
        except Exception as e:
            self.logger.error(f"Failed to load trader state: {e}")
            raise e

        self.logger.info(f"Trader parameters and state loaded from {path}")
