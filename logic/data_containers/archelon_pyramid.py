from common.enums.decision import Decision
from common.enums.position import Position
from logic.data_containers.trader_state import TradeState


class ArchelonPyramid:
    def __init__(self, logger):
        self.logger = logger
        self.active_stop_loss = 0
        self.pyramid_count = 0
        self.pyramid_entries = []
        self.pyramid_stop_losses = []
        self.last_init_timestamp = None

    def __str__(self):
        return f"Pyramid Count: {self.pyramid_count}, Entries: {self.pyramid_entries}, Stop Losses: {self.pyramid_stop_losses}"

    def calculate_pyramid_stop_loss(self, current_price, entry_stop_loss, atr_value, decision):
        position = 'LONG' if decision == Decision.buy else 'SHORT' if decision == Decision.sell else 'NONE'
        self.pyramid_count = 0

        if decision == Decision.buy:
            # Include initial entry price as first element, then pyramid target prices
            self.pyramid_entries = [
                current_price,  # Initial entry price (will be updated with actual executed price)
                current_price + (0.5 * atr_value),  # Pyramid level 1 target
                current_price + (1.0 * atr_value),  # Pyramid level 2 target
                current_price + (1.5 * atr_value)   # Pyramid level 3 target
            ]
            self.pyramid_stop_losses = [
                entry_stop_loss,
                entry_stop_loss + (0.5 * atr_value),
                entry_stop_loss + (1.0 * atr_value),
                entry_stop_loss + (1.5 * atr_value)
            ]
        elif decision == Decision.sell:
            # Include initial entry price as first element, then pyramid target prices
            self.pyramid_entries = [
                current_price,  # Initial entry price (will be updated with actual executed price)
                current_price - (0.5 * atr_value),  # Pyramid level 1 target
                current_price - (1.0 * atr_value),  # Pyramid level 2 target
                current_price - (1.5 * atr_value)   # Pyramid level 3 target
            ]
            self.pyramid_stop_losses = [
                entry_stop_loss,
                entry_stop_loss - (0.5 * atr_value),
                entry_stop_loss - (1.0 * atr_value),
                entry_stop_loss - (1.5 * atr_value)
            ]

        self.logger.debug(f"[Pyramid] Initialized {position} position - "
                  f"Initial Entry: {current_price:.4f}, ATR: {atr_value:.4f}, "
                  f"Initial Stop: {self.pyramid_stop_losses[0]:.4f}")
        for i, (entry, stop) in enumerate(zip(self.pyramid_entries[1:], self.pyramid_stop_losses[1:]), 1):
            self.logger.debug(f"[Pyramid] Level {i} Target - Entry: {entry:.4f}, Stop: {stop:.4f}")

    def reset(self):
        if self.pyramid_count > 0:  
            self.logger.info(f"[Pyramid] Resetting pyramid from level {self.pyramid_count}")
        self.active_stop_loss = 0
        self.pyramid_count = 0
        self.pyramid_entries = []
        self.pyramid_stop_losses = []
        self.last_init_timestamp = None

    def get_pyramid_entry(self):
        # Get the next pyramid level entry (pyramid_count + 1 since index 0 is initial entry)
        next_pyramid_index = self.pyramid_count + 1
        if next_pyramid_index < len(self.pyramid_entries):
            return self.pyramid_entries[next_pyramid_index]
        else:
            return None

    def get_pyramid_stop_loss(self):
        if self.pyramid_count == 0:
            return None
        return self.pyramid_stop_losses[self.pyramid_count]

    def increase_pyramid_count(self):
        if self.pyramid_count < 3:
            self.pyramid_count += 1

            self.logger.info(f"[Pyramid] Increased to level {self.pyramid_count} - "
                      f"Current Stop: {self.pyramid_stop_losses[self.pyramid_count]:.4f}")

    def check_pyramid(self, current_state: TradeState) -> Decision:
        position = current_state.current_active_position
        current_price = current_state.current_price
        if self.pyramid_count >= 3:
            return Decision.do_nothing

        # Check against the next pyramid level target (pyramid_count + 1)
        next_pyramid_index = self.pyramid_count + 1
        if next_pyramid_index >= len(self.pyramid_entries):
            return Decision.do_nothing

        if position == Position.long:
            if current_price >= self.pyramid_entries[next_pyramid_index]:
                return Decision.pyramid
        elif position == Position.short:
            if current_price <= self.pyramid_entries[next_pyramid_index]:
                return Decision.pyramid
        return Decision.do_nothing
