import json
import os

from common.enums.time_interval import TimeInterval


class TraderParameters:
    CONFIG_DIR = "./config/traders"

    def __init__(self,
                 token: str = "",
                 interval: TimeInterval = TimeInterval.ONE_HOUR,
                 amount_from_config: float = 10.0,
                 leverage: int = 20,
                 atr_multiplier: float = 2.0,
                 supertrend_atr: float = 2.0,
                 trailing_limit: int = 40,
                 trailing_distance: int = 60,
                 basic_stop: float = -20,
                 n_ratio: float = 0.004,
                 position_preference: str = 'both'
                 ):
        self.token = token
        self.interval = interval
        self.amount_from_config = amount_from_config
        self.leverage = leverage
        self.atr_multiplier = atr_multiplier
        self.supertrend_atr = supertrend_atr
        self.trailing_limit = trailing_limit
        self.trailing_distance = trailing_distance
        self.basic_stop = basic_stop
        self.n_ratio = n_ratio
        self.position_preference = position_preference

    def to_dict(self) -> dict:
        return {
            "token": self.token,
            "interval": self.interval.value,
            "amount_from_config": self.amount_from_config,
            "leverage": self.leverage,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "TraderParameters":
        # map the interval string back to the enum
        interval_map = {
            ti.value: ti for ti in TimeInterval
        }
        interval = interval_map.get(data.get("interval"),
                                    TimeInterval.ONE_HOUR)
        return cls(
            token=data.get("token", ""),
            interval=interval,
            amount_from_config=data.get("amount_from_config", 10.0),
            leverage=data.get("leverage", 20)
        )

