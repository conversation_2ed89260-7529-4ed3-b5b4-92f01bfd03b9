from common.enums.decision import Decision
from common.enums.position import Position


class ArchelonState:
    def __init__(self):
        self.current_archelon_position = Position.notr
        self.current_archelon_decision = Decision.do_nothing
        self.pyramid_decision = Decision.do_nothing

    def reset(self):
        self.current_archelon_position = Position.notr
        self.current_archelon_decision = Decision.do_nothing
        self.pyramid_decision = Decision.do_nothing

    def update_state(self, decision: Decision):
        if self.current_archelon_position == Position.notr:
            if decision == Decision.buy:
                self.current_archelon_position = Position.long
            elif decision == Decision.sell:
                self.current_archelon_position = Position.short
        elif self.current_archelon_position != Position.notr:
            if decision in [Decision.market_exit, Decision.pyramid_exit]:
                self.current_archelon_position = Position.notr

        if decision == Decision.pyramid:
            self.pyramid_decision = Decision.pyramid
        elif decision == Decision.pyramid_exit:
            self.pyramid_decision = Decision.pyramid_exit

    def get_position(self) -> Position:
        return self.current_archelon_position


class SupertrendState:
    def __init__(self):
        self.current_supertrend_position = Position.notr
        self.current_supertrend_decision = Decision.do_nothing

    def reset(self):
        self.current_supertrend_position = Position.notr
        self.current_supertrend_decision = Decision.do_nothing

    def update_state(self, decision: Decision):
        if decision == Decision.sell and self.current_supertrend_position == Position.notr:
            self.current_supertrend_position = Position.short

        elif decision == Decision.buy and self.current_supertrend_position == Position.notr:
            self.current_supertrend_position = Position.long

        elif decision == Decision.buy and self.current_supertrend_position == Position.short:
            self.current_supertrend_position = Position.long

        elif decision == Decision.sell and self.current_supertrend_position == Position.long:
            self.current_supertrend_position = Position.short


    def get_position(self) -> Position:
        return self.current_supertrend_position



class TradeState:

    def __init__(
            self, current_active_position: Position = Position.notr,
            current_price: float = 0.0, amount: float = 0.0,
            last_atr_exit_decision: Decision = Decision.do_nothing,
            archelon_state: ArchelonState = ArchelonState(),
            supertrend_state: SupertrendState = SupertrendState()
    ):
        self.current_active_position = current_active_position
        self.max_profit = 0
        self.current_profit = 0

        self.archelon_state = archelon_state
        self.supertrend_state = supertrend_state

        self.last_atr_exit_decision = last_atr_exit_decision
        self.current_price = current_price
        self.amount = amount
        self.entry_price = 0

    def reset(self):
        self.archelon_state.reset()
        self.supertrend_state.reset()
        self.last_atr_exit_decision = Decision.do_nothing
        self.current_active_position = Position.notr
        self.max_profit = 0
        self.current_profit = 0
        self.entry_price = 0


