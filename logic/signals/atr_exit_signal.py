from common.enums.decision import Decision
from common.enums.position import Position
from logic.data_containers.trader_parameters import TraderParameters
from logic.data_containers.trader_state import TradeState
from logic.signals.signal_base import SignalBase


class AtrExitSignal(SignalBase):

    def __init__(self, trade_params: TraderParameters):
        super().__init__()
        self.trade_params = trade_params
        self.atr_exit_value = 0.0

    def initialize(self, atr_value: float, trade_state: TradeState):
        entry_value = trade_state.current_price
        if trade_state.current_active_position == Position.long:
            self.atr_exit_value = entry_value - 2 * atr_value
            self.atr_exit_value = entry_value - 2 * atr_value

        elif trade_state.current_active_position == Position.short:
            self.atr_exit_value = entry_value + 2 * atr_value

    def run_signal(self, trade_state: TradeState) -> Decision:
        current_price = trade_state.current_price
        current_position = trade_state.current_active_position

        if current_position == Position.long and current_price <= self.atr_exit_value:
            return Decision.market_exit

        elif current_position == Position.short and current_price >= self.atr_exit_value:
            return Decision.market_exit
        else:
            #if current_position == Position.notr:
            return Decision.do_nothing
