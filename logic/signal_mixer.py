from common.enums.decision import Decision
from common.enums.position import Position
from logic.data_containers.trader_state import TradeState


class SignalMixer:

    def __init__(self, logger=None):
        self.logger = logger

    def combine_signals(self, trade_state: TradeState) -> Decision:
        archelon_position = trade_state.archelon_state.get_position()
        supertrend_position = trade_state.supertrend_state.get_position()

        atr_exit_decision = trade_state.last_atr_exit_decision
        archelon_pyramid_decision = trade_state.archelon_state.pyramid_decision
        if atr_exit_decision == Decision.market_exit:
            self.logger.info("[SignalMixer] ATR exit signal triggered market exit")
            return Decision.market_exit
        # elif archelon_pyramid_decision == Decision.pyramid_exit:
        #     self.logger.info("[SignalMixer] PYRAMID exit signal triggered market exit")
        #     return Decision.market_exit

        self.logger.debug(f"""COMBINE SIGNAL WORKING ----
            [SignalMixer] Combining signals - Current position: {trade_state.current_active_position})
            Archelon: {archelon_position}, SuperTrend: {supertrend_position}, 
                ATR exit: {atr_exit_decision}, Pyramid: {archelon_pyramid_decision}
        """)
        decision = Decision.do_nothing

        if trade_state.current_active_position != Position.notr:
            if archelon_position == Position.notr and supertrend_position != trade_state.current_active_position:
                self.logger.info(f"""
                            [SignalMixer] Exit condition met: 
                            Archelon is neutral, Archelon pyramid exit ({archelon_pyramid_decision}) 
                            SuperTrend ({supertrend_position}) current position ({trade_state.current_active_position})
                            """)
                return Decision.market_exit
            elif archelon_pyramid_decision == Decision.pyramid:
                self.logger.info("[SignalMixer] Archelon pyramid signal triggered pyramid entry")
                return Decision.pyramid
            else:
                return Decision.do_nothing
        elif trade_state.current_active_position == Position.notr:
            if archelon_position == Position.long and supertrend_position == Position.long:
                self.logger.info("[SignalMixer] Both Archelon and SuperTrend signals are LONG - triggering BUY")
                return Decision.buy

            elif ((archelon_position == Position.short) and
                  (supertrend_position == Position.short)):
                self.logger.info("[SignalMixer] Both Archelon and SuperTrend signals are SHORT - triggering SELL")
                return Decision.sell
            else:
                self.logger.debug(f"[SignalMixer] No matching signal combination found")
                return decision
        else:
            raise ValueError(f"Invalid position: {trade_state.current_active_position}")

