# super-archelon


super archelon gonna fill these later 

# How to run a bot

- create a machine we use ubuntu t2.micro or t3.micro 8gb default values
- get the connection string
- go to the folder that the pem file exists and connect to the machine
- after that run these commands:
```commandline
sudo apt update
sudo apt install docker.io
```
- create a token in github for cloning repo
- after that run these commands:
```commandline
git config --global credential.helper store
git clone https://github.com/FFAutomaton/super-archelon.git
```
- enter github user name and token you generated
- enter the repo dir
- create a file called config `sudo nano config.py`
```commandline
API_KEY="xxxx"
API_SECRET="xxx"
```
- save the file ctrl+x, enter, enter
- update the run.sh
```commandline
sudo nano bash_scripts/run.sh
```
- make sure the json file exist with the true configurations under `config/ENAUSDT`
```commandline
{
    "interval": "15m",
    "amount_from_config": 20,
    "leverage": 20,
    "atr_multiplier": 1.5,
    "supertrend_atr": 3.5,
    "trailing_limit": 40,
    "trailing_distance": 60,
    "basic_stop": -20,
    "n_ratio": 0.004,
    "position_preference": "both"
}
```
- run this command to run the bot in the repo main directory
```commandline
sudo bash bash_scripts/run.sh
```
- if you want to reset the bot, stop docker, delete the files updated,
.pkl state file and log files.
```commandline
sudo docker stop $(sudo docker ps -q)
git reset --hard
sudo rm config/traders/*.pkl
sudo rm log_files/*.log
```