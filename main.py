import json
import os
import time
import traceback
from common.static_logger import get_logger_
from common.time_decorators import fifteen_mins_loop, two_seconds_loop, one_hour_loop, six_hours_loop
from common.enums.time_interval import TimeInterval

from config import API_KEY, API_SECRET
from logic.data_containers.trader_parameters import TraderParameters
from logic.trader import SuperArchelon
from services.email_service import FFEmailSender
from services.exchange_services.binance_exchange import BinanceExchange

email_sender_instance = FFEmailSender(
    email_address="<EMAIL>",
    email_password="zwcukggmopcokzzp"
)


@two_seconds_loop
def smaller_loop(this_second, trader):
    trader.instant_loop()
    return trader


@fifteen_mins_loop
def data_loop(this_second, trader):
    trader.logger.debug(msg="15m loop running")
    trader.interval_loop()
    return trader


@six_hours_loop
def run_6hours_loop(this_second, trader):
    _info = trader.exchange.get_binance_spot_quantity_precisions()

    filename = "./config/quantity_precision_info.json"
    if os.path.exists(filename):
        with open(filename, "w") as write_file:
            json.dump(_info, write_file)
    else:
        with open(filename, "w+") as read_file:
            json.dump(_info, read_file)


def manage_running_traders(this_second, trader):
    try:
        trader = data_loop(this_second, trader)
        trader = smaller_loop(this_second, trader)
        run_6hours_loop(this_second, trader)
        trader.save_trader()
    except Exception as e:
        trader.save_trader()
        raise e


def send_error_email(exception: Exception, account: str = None, token: str = None):
    subject = f" {account} SuperArchelon shit the bed: {type(exception).__name__}"
    message = f"{token} SuperArchelon trading bot:\n\n{str(exception)}\n\n{traceback.format_exc()}"

    # Email recipients
    # recipients = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    # recipients = ["<EMAIL>"]
    recipients = ["<EMAIL>"]
    email_sender_instance.send_email(recipients, subject, message)


def load_initial_params(token: str):
    filename = f"./config/{token}.json"
    if os.path.exists(filename):
        with open(filename, "r") as read_file:
            return json.load(read_file)
    else:
        return {
            "interval": "1h",
            "amount_from_config": 1,
            "leverage": 3,
            "atr_multiplier": 2.0,
            "supertrend_atr": 3.0,
            "trailing_limit": 40,
            "trailing_distance": 60,
            "basic_stop": -20,
            "n_ratio": 0.004,
            "position_preference": "both"
        }


if __name__ == '__main__':
    try:
        LOOP_SECS = 2
        TOKEN = os.environ.get("TOKEN", "AVAXUSDT")
        ACCOUNT = os.environ.get("BOT_ACCOUNT", "Sevki")
        ACCOUNT = ACCOUNT + "_" + TOKEN
        # TOKEN = os.environ.get("TOKEN", "example")
        print(f"Super archelon starting for {TOKEN} {ACCOUNT}")

        # Create logger first so we can use it for loading parameters
        logger = get_logger_(TOKEN)
        # logger.setLevel(logging.DEBUG)
        logger.info(f"Starting SuperArchelon for {TOKEN} {ACCOUNT}")

        # Initialize API client
        secrets = {"api_key": API_KEY, "api_secret": API_SECRET}
        exchange = BinanceExchange(secrets, logger)

        # Load parameters from JSON file
        params = load_initial_params(TOKEN)

        # Add token to params and convert interval
        params['token'] = TOKEN

        params['interval'] = TimeInterval(params.get("interval", "1h"))

        trade_params = TraderParameters(**params)
        trader = SuperArchelon(TOKEN, logger, exchange, trade_params)
        # Load trading parameters from configuration file
        trader.load()
        # TODO: decrease save operations
        # Initial run of the interval loop
        trader.interval_loop()

        last_run_time = 0
        # Main loop
        while True:
            try:
                this_second = int(time.time())

                # Check if it's time to run or if we've missed a cycle
                if this_second % LOOP_SECS == 0 or (this_second - last_run_time) >= LOOP_SECS:
                    manage_running_traders(this_second, trader)
                    last_run_time = this_second

                # More precise sleep calculation to reduce drift
                current_time = time.time()
                next_execution = (int(current_time) // LOOP_SECS + 1) * LOOP_SECS
                sleep_time = max(0.01, next_execution - current_time)  # At least 10ms sleep
                time.sleep(sleep_time)
            except Exception as e:
                trader.logger.error(msg=f"Main loop error: {str(e)}")
                trader.logger.error(msg=f"Exception args: {e.args}")
                trader.logger.error(msg=f"Exception details: {e}")
                traceback.print_exc()
                raise e

    except Exception as e:
        print(f"Fatal error: {e}")
        send_error_email(e, ACCOUNT)
        traceback.print_exc()
